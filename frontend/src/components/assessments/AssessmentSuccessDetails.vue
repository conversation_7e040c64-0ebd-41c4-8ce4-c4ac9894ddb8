<template>
  <section class="bg-green-900/20 backdrop-blur-sm border border-green-500/30 rounded-xl p-8">
    <h2 class="text-xl font-semibold text-green-400 mb-6 flex items-center">
      <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path
          fill-rule="evenodd"
          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
          clip-rule="evenodd"
        />
      </svg>
      Assessment Created Successfully!
    </h2>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Assessment Details -->
      <div class="space-y-4">
        <div>
          <h3 class="text-lg font-medium text-white mb-2">Assessment Details</h3>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-white/60">Name:</span>
              <span class="text-white font-medium">{{ assessment.name }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60">ID:</span>
              <span class="text-white font-mono">{{ assessment.assessment_id }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60">Duration:</span>
              <span class="text-white">{{ assessment.duration }} minutes</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60">Mode:</span>
              <span class="text-white capitalize">{{ assessment.question_selection_mode }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-white/60">Status:</span>
              <span class="text-green-400 font-medium">Active</span>
            </div>
          </div>
        </div>

        <!-- Skills Information -->
        <div v-if="assessment.skills && assessment.skills.length > 0">
          <h3 class="text-lg font-medium text-white mb-2">Associated Skills</h3>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="skill in assessment.skills"
              :key="skill.id"
              class="px-3 py-1 bg-phantom-blue/20 text-phantom-blue border border-phantom-blue/30 rounded-full text-xs"
            >
              {{ skill.name }}
            </span>
          </div>
        </div>
      </div>

      <!-- Next Steps -->
      <div class="space-y-4">
        <div>
          <h3 class="text-lg font-medium text-white mb-2">Next Steps</h3>
          <div class="space-y-3">
            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-phantom-blue/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span class="text-phantom-blue text-sm font-bold">1</span>
              </div>
              <div class="text-sm text-white/80">
                <p class="font-medium">View Assessment</p>
                <p class="text-white/60">Review the assessment details and settings</p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-phantom-blue/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span class="text-phantom-blue text-sm font-bold">2</span>
              </div>
              <div class="text-sm text-white/80">
                <p class="font-medium">
                  {{ assessment.question_selection_mode === 'fixed' ? 'Add Fixed Questions' : 'Create Session' }}
                </p>
                <p class="text-white/60">
                  {{ assessment.question_selection_mode === 'fixed'
                    ? 'Select specific questions for this assessment'
                    : 'Start creating sessions for this assessment'
                  }}
                </p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-phantom-blue/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span class="text-phantom-blue text-sm font-bold">3</span>
              </div>
              <div class="text-sm text-white/80">
                <p class="font-medium">Share Assessment</p>
                <p class="text-white/60">Generate links for candidates to take the assessment</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3 pt-4">
          <button
            class="btn-phantom flex-1 text-sm py-2 px-4"
            @click="viewAssessment"
          >
            View Assessment
          </button>
          <button
            v-if="assessment.question_selection_mode === 'fixed'"
            class="btn-phantom-secondary flex-1 text-sm py-2 px-4"
            @click="addFixedQuestions"
          >
            Add Questions
          </button>
          <button
            v-else
            class="btn-phantom-secondary flex-1 text-sm py-2 px-4"
            @click="createSession"
          >
            Create Session
          </button>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
/**
 * Assessment Success Details Component
 * Shows success information and next steps after assessment creation
 */
import { useNavigation } from '@/composables';
import { logUserAction } from '@/utils/logger';

const props = defineProps({
  assessment: {
    type: Object,
    required: true
  }
});

const navigation = useNavigation();

// Action handlers
const viewAssessment = () => {
  logUserAction('view_created_assessment', {
    assessmentId: props.assessment.assessment_id
  });
  navigation.navigateTo(`/assessment-details/${props.assessment.assessment_id}`);
};

const addFixedQuestions = () => {
  logUserAction('add_fixed_questions', {
    assessmentId: props.assessment.assessment_id
  });
  navigation.navigateTo(`/assessment-details/${props.assessment.assessment_id}?tab=questions`);
};

const createSession = () => {
  logUserAction('create_session_from_success', {
    assessmentId: props.assessment.assessment_id
  });
  navigation.navigateTo(`/create-session?assessment_id=${props.assessment.assessment_id}`);
};
</script>

<style scoped>
.btn-phantom {
  @apply bg-gradient-to-r from-phantom-blue to-phantom-indigo text-white font-medium rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-phantom-blue/25;
}

.btn-phantom-secondary {
  @apply bg-white/5 backdrop-blur-sm border border-white/10 text-white font-medium rounded-lg transition-all duration-200 hover:bg-white/10 hover:border-white/20;
}
</style>
