/**
 * Assessment Form Composable
 * 
 * Provides reusable form logic for assessment creation and editing
 * Reduces code duplication and centralizes form validation
 */
import { ref, computed } from 'vue';
import { api } from '@/services/api';
import { useMessageHandler } from '@/utils/messageHandler';
import { useLoadingState } from './useUIState';
import { 
  extractResponseData, 
  extractErrorInfo 
} from '@/utils/apiResponseHandler';
import { 
  logApiRequest, 
  logApiResponse, 
  logApiError,
  logUserAction 
} from '@/utils/logger';

export function useAssessmentForm() {
  const loadingState = useLoadingState();
  const messageHandler = useMessageHandler();

  // Form state
  const formData = ref({
    name: '',
    description: '',
    duration: 30,
    selectedSkillIds: [],
    questionSelectionMode: 'dynamic'
  });

  // Skills data
  const skills = ref([]);
  const skillsLoading = ref(false);

  // Validation state
  const hasInteractedWithSkills = ref(false);
  const errors = ref({});

  // Computed properties
  const validSelectedSkillIds = computed(() => {
    return formData.value.selectedSkillIds.filter(
      (id) => id !== null && id !== undefined && id !== ""
    );
  });

  const isFormValid = computed(() => {
    return (
      formData.value.name?.trim() &&
      formData.value.description?.trim() &&
      formData.value.duration > 0 &&
      validSelectedSkillIds.value.length > 0
    );
  });

  /**
   * Validate form data
   */
  const validateForm = () => {
    const newErrors = {};

    if (!formData.value.name?.trim()) {
      newErrors.name = 'Assessment name is required';
    }

    if (!formData.value.description?.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.value.description.trim().length < 20) {
      newErrors.description = 'Description must be at least 20 characters';
    }

    if (!formData.value.duration || formData.value.duration < 5) {
      newErrors.duration = 'Duration must be at least 5 minutes';
    } else if (formData.value.duration > 180) {
      newErrors.duration = 'Duration cannot exceed 180 minutes';
    }

    if (validSelectedSkillIds.value.length === 0) {
      newErrors.skills = 'Please select at least one skill';
    }

    errors.value = newErrors;
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Fetch available skills
   */
  const fetchSkills = async () => {
    skillsLoading.value = true;
    messageHandler.clearMessage();

    try {
      logApiRequest('GET', '/skills');
      const response = await api.admin.getSkills();
      logApiResponse('GET', '/skills', response.status);
      
      const responseData = extractResponseData(response);
      skills.value = responseData?.data || [];
      
      return skills.value;
    } catch (error) {
      logApiError('GET', '/skills', error);
      const errorInfo = extractErrorInfo(error);
      messageHandler.setErrorMessage(
        errorInfo.message || 'Failed to fetch skills'
      );
      throw error;
    } finally {
      skillsLoading.value = false;
    }
  };

  /**
   * Get skill name by ID
   */
  const getSkillName = (skillId) => {
    if (!Array.isArray(skills.value) || !skillId) {
      return `Skill ${skillId}`;
    }

    const skill = skills.value.find(s => s.id === skillId || s.numericId === skillId);
    return skill?.name || `Skill ${skillId}`;
  };

  /**
   * Add skill to selection
   */
  const addSkill = (skillId) => {
    if (!formData.value.selectedSkillIds.includes(skillId)) {
      formData.value.selectedSkillIds.push(skillId);
      hasInteractedWithSkills.value = true;
      
      logUserAction('skill_added', { skillId, skillName: getSkillName(skillId) });
    }
  };

  /**
   * Remove skill from selection
   */
  const removeSkill = (skillId) => {
    formData.value.selectedSkillIds = formData.value.selectedSkillIds.filter(
      id => id !== skillId
    );
    hasInteractedWithSkills.value = true;
    
    logUserAction('skill_removed', { skillId, skillName: getSkillName(skillId) });
  };

  /**
   * Clear all selected skills
   */
  const clearSkillSelection = () => {
    const previousCount = formData.value.selectedSkillIds.length;
    formData.value.selectedSkillIds = [];
    hasInteractedWithSkills.value = true;
    
    logUserAction('skills_cleared', { previousCount });
  };

  /**
   * Reset form to initial state
   */
  const resetForm = () => {
    formData.value = {
      name: '',
      description: '',
      duration: 30,
      selectedSkillIds: [],
      questionSelectionMode: 'dynamic'
    };
    
    hasInteractedWithSkills.value = false;
    errors.value = {};
    messageHandler.clearMessage();
    
    logUserAction('form_reset');
  };

  /**
   * Submit assessment form
   */
  const submitAssessment = async () => {
    if (!validateForm()) {
      messageHandler.setErrorMessage('Please fix the form errors before submitting');
      return null;
    }

    loadingState.startLoading('Creating assessment...');

    try {
      const payload = {
        name: formData.value.name.trim(),
        description: formData.value.description.trim(),
        duration: formData.value.duration,
        skill_ids: validSelectedSkillIds.value,
        question_selection_mode: formData.value.questionSelectionMode
      };

      logApiRequest('POST', '/assessments', payload);
      const response = await api.admin.createAssessment(payload);
      logApiResponse('POST', '/assessments', response.status);

      const responseData = extractResponseData(response);
      
      messageHandler.setSuccessMessage(
        `Successfully created "${formData.value.name}" assessment!`
      );
      
      logUserAction('assessment_created', { 
        name: formData.value.name,
        skillCount: validSelectedSkillIds.value.length,
        mode: formData.value.questionSelectionMode
      });

      return responseData;
    } catch (error) {
      logApiError('POST', '/assessments', error);
      const errorInfo = extractErrorInfo(error);
      
      messageHandler.setErrorMessage(
        errorInfo.message || 'Failed to create assessment'
      );
      
      throw error;
    } finally {
      loadingState.stopLoading();
    }
  };

  return {
    // State
    formData,
    skills,
    skillsLoading,
    hasInteractedWithSkills,
    errors,
    
    // Computed
    validSelectedSkillIds,
    isFormValid,
    
    // Loading and messaging
    ...loadingState,
    ...messageHandler,
    
    // Methods
    validateForm,
    fetchSkills,
    getSkillName,
    addSkill,
    removeSkill,
    clearSkillSelection,
    resetForm,
    submitAssessment
  };
}