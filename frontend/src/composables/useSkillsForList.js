/**
 * Composable for managing skills data for lists
 */
import { ref } from 'vue';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { extractResponseData, extractErrorInfo } from '@/utils/apiResponseHandler';
import { useLoadingState } from './useUIState';

export function useSkillsForList() {
  const loadingState = useLoadingState();
  const { message, isSuccess, setErrorMessage, clearMessage } = useMessageHandler();

  const skills = ref([]);
  const totalSkills = ref(0);

  // Fetch skills
  const fetchSkills = async (page = 1, itemsPerPage = 100) => {
    loadingState.startLoading('Loading skills...');
    clearMessage();

    try {
      const offset = (page - 1) * itemsPerPage;
      const params = {
        limit: itemsPerPage,
        offset: offset,
      };
      const response = await api.admin.getSkills(params);
      const skillsData = extractResponseData(response);

      if (skillsData) {
        // Assuming skillsData is an array of skills
        skills.value = skillsData;
        totalSkills.value = response.data?.meta?.pagination?.total || skillsData.length;
        await fetchQuestionCounts(skillsData);
      } else {
        skills.value = [];
        totalSkills.value = 0;
        setErrorMessage('No skill data received from server');
      }
    } catch (error) {
      logError(error, 'fetchSkills');
      const errorInfo = extractErrorInfo(error);
      setErrorMessage(
        errorInfo.message || 'An unexpected error occurred while fetching skills'
      );
    } finally {
      loadingState.stopLoading();
    }
  };

  // Fetch question counts for each skill
  const fetchQuestionCounts = async (skillsData) => {
    try {
      const response = await api.admin.getSkillQuestionCounts();
      const questionCounts = extractResponseData(response);

      if (questionCounts) {
        skills.value = skillsData.map((skill) => ({
          ...skill,
          questionCount: questionCounts[skill.id] || 0,
        }));
      } else {
        skills.value = skillsData;
      }
    } catch (error) {
      const errorInfo = extractErrorInfo(error);
      logError(error, 'fetchQuestionCounts');
      skills.value = skillsData; // Still set skills even if question counts fail
    }
  };

  return {
    ...loadingState,
    message,
    isSuccess,
    setErrorMessage,
    clearMessage,
    skills,
    totalSkills,
    fetchSkills,
  };
}
