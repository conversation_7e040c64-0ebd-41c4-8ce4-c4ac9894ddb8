/**
 * Event Handling Utilities Composable
 *
 * Provides reusable event handling patterns with:
 * - Keyboard event handlers
 * - Click outside detection
 * - Window resize handling
 * - Scroll event handling
 * - Automatic cleanup
 */
import { ref, onMounted, onUnmounted } from 'vue';

/**
 * Keyboard event handling
 */
export function useKeyboardEvents(options = {}) {
  const {
    target = document,
    preventDefault = true,
    stopPropagation = false,
  } = options;

  const pressedKeys = ref(new Set());
  const keyHandlers = ref(new Map());

  const handleKeyDown = (event) => {
    pressedKeys.value.add(event.key);

    const handler = keyHandlers.value.get(event.key);
    if (handler) {
      if (preventDefault) event.preventDefault();
      if (stopPropagation) event.stopPropagation();
      handler(event);
    }
  };

  const handleKeyUp = (event) => {
    pressedKeys.value.delete(event.key);
  };

  const addKeyHandler = (key, handler) => {
    keyHandlers.value.set(key, handler);
  };

  const removeKeyHandler = (key) => {
    keyHandlers.value.delete(key);
  };

  const isKeyPressed = (key) => {
    return pressedKeys.value.has(key);
  };

  const cleanup = () => {
    target.removeEventListener('keydown', handleKeyDown);
    target.removeEventListener('keyup', handleKeyUp);
    pressedKeys.value.clear();
    keyHandlers.value.clear();
  };

  onMounted(() => {
    target.addEventListener('keydown', handleKeyDown);
    target.addEventListener('keyup', handleKeyUp);
  });

  onUnmounted(cleanup);

  return {
    pressedKeys,
    addKeyHandler,
    removeKeyHandler,
    isKeyPressed,
    cleanup,
  };
}

/**
 * Escape key handler (commonly used for modals)
 */
export function useEscapeKey(handler, options = {}) {
  const { enabled = true } = options;

  const keyboardEvents = useKeyboardEvents();

  const enableEscapeHandler = () => {
    keyboardEvents.addKeyHandler('Escape', handler);
  };

  const disableEscapeHandler = () => {
    keyboardEvents.removeKeyHandler('Escape');
  };

  if (enabled) {
    enableEscapeHandler();
  }

  return {
    enableEscapeHandler,
    disableEscapeHandler,
  };
}

/**
 * Click outside detection
 */
export function useClickOutside(elementRef, handler, options = {}) {
  const {
    enabled = true,
    capture = true,
  } = options;

  const handleClickOutside = (event) => {
    if (!enabled || !elementRef.value) return;

    if (!elementRef.value.contains(event.target)) {
      handler(event);
    }
  };

  const enable = () => {
    document.addEventListener('click', handleClickOutside, capture);
  };

  const disable = () => {
    document.removeEventListener('click', handleClickOutside, capture);
  };

  const cleanup = () => {
    disable();
  };

  onMounted(() => {
    if (enabled) {
      enable();
    }
  });

  onUnmounted(cleanup);

  return {
    enable,
    disable,
    cleanup,
  };
}

/**
 * Window resize handling
 */
export function useWindowResize(handler, options = {}) {
  const {
    debounceMs = 100,
    immediate = true,
  } = options;

  const windowSize = ref({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  let resizeTimeout = null;

  const handleResize = () => {
    if (resizeTimeout) {
      clearTimeout(resizeTimeout);
    }

    resizeTimeout = setTimeout(() => {
      windowSize.value = {
        width: window.innerWidth,
        height: window.innerHeight,
      };

      if (handler) {
        handler(windowSize.value);
      }
    }, debounceMs);
  };

  const cleanup = () => {
    window.removeEventListener('resize', handleResize);
    if (resizeTimeout) {
      clearTimeout(resizeTimeout);
    }
  };

  onMounted(() => {
    window.addEventListener('resize', handleResize);

    if (immediate && handler) {
      handler(windowSize.value);
    }
  });

  onUnmounted(cleanup);

  return {
    windowSize,
    cleanup,
  };
}

/**
 * Scroll event handling
 */
export function useScroll(elementRef, handler, options = {}) {
  const {
    throttleMs = 16, // ~60fps
    target = null, // null means use elementRef, 'window' means window
  } = options;

  const scrollPosition = ref({ x: 0, y: 0 });
  let scrollTimeout = null;

  const handleScroll = (event) => {
    if (scrollTimeout) return;

    scrollTimeout = setTimeout(() => {
      const element = target === 'window' ? window : (elementRef?.value || event.target);

      if (target === 'window') {
        scrollPosition.value = {
          x: window.scrollX,
          y: window.scrollY,
        };
      } else if (element) {
        scrollPosition.value = {
          x: element.scrollLeft,
          y: element.scrollTop,
        };
      }

      if (handler) {
        handler(scrollPosition.value, event);
      }

      scrollTimeout = null;
    }, throttleMs);
  };

  const cleanup = () => {
    const element = target === 'window' ? window : elementRef?.value;
    if (element) {
      element.removeEventListener('scroll', handleScroll);
    }

    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }
  };

  onMounted(() => {
    const element = target === 'window' ? window : elementRef?.value;
    if (element) {
      element.addEventListener('scroll', handleScroll, { passive: true });
    }
  });

  onUnmounted(cleanup);

  return {
    scrollPosition,
    cleanup,
  };
}

/**
 * Mouse position tracking
 */
export function useMousePosition(options = {}) {
  const {
    throttleMs = 16,
    relative = false, // relative to element or absolute to viewport
    elementRef = null,
  } = options;

  const mousePosition = ref({ x: 0, y: 0 });
  let mouseTimeout = null;

  const handleMouseMove = (event) => {
    if (mouseTimeout) return;

    mouseTimeout = setTimeout(() => {
      if (relative && elementRef?.value) {
        const rect = elementRef.value.getBoundingClientRect();
        mousePosition.value = {
          x: event.clientX - rect.left,
          y: event.clientY - rect.top,
        };
      } else {
        mousePosition.value = {
          x: event.clientX,
          y: event.clientY,
        };
      }

      mouseTimeout = null;
    }, throttleMs);
  };

  const cleanup = () => {
    document.removeEventListener('mousemove', handleMouseMove);
    if (mouseTimeout) {
      clearTimeout(mouseTimeout);
    }
  };

  onMounted(() => {
    document.addEventListener('mousemove', handleMouseMove, { passive: true });
  });

  onUnmounted(cleanup);

  return {
    mousePosition,
    cleanup,
  };
}

/**
 * Focus trap for accessibility
 */
export function useFocusTrap(elementRef, options = {}) {
  const {
    enabled = true,
    initialFocus = null,
    returnFocus = true,
  } = options;

  let previousActiveElement = null;
  const focusableElements = ref([]);

  const getFocusableElements = () => {
    if (!elementRef.value) return [];

    const selectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
    ];

    return Array.from(elementRef.value.querySelectorAll(selectors.join(', ')));
  };

  const handleKeyDown = (event) => {
    if (!enabled || event.key !== 'Tab') return;

    const elements = getFocusableElements();
    if (elements.length === 0) return;

    const firstElement = elements[0];
    const lastElement = elements[elements.length - 1];

    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  };

  const activate = () => {
    if (!elementRef.value) return;

    previousActiveElement = document.activeElement;
    focusableElements.value = getFocusableElements();

    // Set initial focus
    if (initialFocus) {
      initialFocus.focus();
    } else if (focusableElements.value.length > 0) {
      focusableElements.value[0].focus();
    }

    document.addEventListener('keydown', handleKeyDown);
  };

  const deactivate = () => {
    document.removeEventListener('keydown', handleKeyDown);

    // Return focus to previous element
    if (returnFocus && previousActiveElement) {
      previousActiveElement.focus();
    }
  };

  const cleanup = () => {
    deactivate();
  };

  onUnmounted(cleanup);

  return {
    activate,
    deactivate,
    cleanup,
  };
}
