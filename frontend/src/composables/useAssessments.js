/**
 * Composable for managing assessments data
 */
import { ref } from 'vue';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { extractResponseData, extractErrorInfo } from '@/utils/apiResponseHandler';
import { useLoadingState } from './useUIState';

export function useAssessments() {
  const loadingState = useLoadingState();
  const { message, isSuccess, setErrorMessage, clearMessage } = useMessageHandler();

  const assessments = ref([]);
  const totalAssessments = ref(0);

  const fetchAssessments = async (page = 1, itemsPerPage = 3) => {
    loadingState.startLoading('Loading assessments...');
    clearMessage();

    try {
      const offset = (page - 1) * itemsPerPage;
      const params = {
        limit: itemsPerPage,
        offset: offset,
      };

      const response = await api.admin.getAssessments(params);
      const responseData = extractResponseData(response);

      if (response.data?.meta?.pagination) {
        assessments.value = responseData || [];
        totalAssessments.value = response.data.meta.pagination.total;
      } else {
        // Fallback for older response format
        const data = extractResponseData(response);
        if (data && data.assessments) {
          assessments.value = data.assessments || [];
          totalAssessments.value = assessments.value.length;
        } else {
          assessments.value = [];
          totalAssessments.value = 0;
          setErrorMessage('No assessment data received from server');
        }
      }
    } catch (error) {
      logError(error, 'fetchAssessments');
      const errorInfo = extractErrorInfo(error);
      setErrorMessage(errorInfo.message || 'Failed to fetch assessments');
    } finally {
      loadingState.stopLoading();
    }
  };

  return {
    ...loadingState,
    message,
    isSuccess,
    setErrorMessage,
    clearMessage,
    assessments,
    totalAssessments,
    fetchAssessments,
  };
}
