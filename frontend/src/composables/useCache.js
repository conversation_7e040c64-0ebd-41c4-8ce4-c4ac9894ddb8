/**
 * Data Caching Composable
 *
 * Provides reusable caching functionality with:
 * - TTL (Time To Live) support
 * - Memory-based caching
 * - Cache invalidation
 * - Cache statistics
 * - Automatic cleanup
 */
import { ref, onUnmounted } from 'vue';
import { debug, warning } from '@/utils/logger';

// Global cache store
const globalCache = new Map();
const cacheTimestamps = new Map();
const cacheStats = ref({
  hits: 0,
  misses: 0,
  sets: 0,
  deletes: 0,
  size: 0
});

export function useCache(options = {}) {
  const {
    defaultTTL = 5 * 60 * 1000, // 5 minutes default
    maxSize = 100,
    keyPrefix = '',
    autoCleanup = true,
    cleanupInterval = 60 * 1000, // 1 minute
  } = options;

  let cleanupTimer = null;

  /**
   * Generate cache key with prefix
   */
  const getCacheKey = (key) => {
    return keyPrefix ? `${keyPrefix}:${key}` : key;
  };

  /**
   * Check if cache entry is expired
   */
  const isExpired = (key) => {
    const timestamp = cacheTimestamps.get(key);
    if (!timestamp) return true;

    return Date.now() - timestamp.time > timestamp.ttl;
  };

  /**
   * Get value from cache
   */
  const get = (key) => {
    const cacheKey = getCacheKey(key);

    if (!globalCache.has(cacheKey)) {
      cacheStats.value.misses++;
      debug('Cache miss', { key: cacheKey });
      return null;
    }

    if (isExpired(cacheKey)) {
      // Remove expired entry
      globalCache.delete(cacheKey);
      cacheTimestamps.delete(cacheKey);
      cacheStats.value.misses++;
      cacheStats.value.size = globalCache.size;
      debug('Cache expired', { key: cacheKey });
      return null;
    }

    cacheStats.value.hits++;
    debug('Cache hit', { key: cacheKey });
    return globalCache.get(cacheKey);
  };

  /**
   * Set value in cache
   */
  const set = (key, value, ttl = defaultTTL) => {
    const cacheKey = getCacheKey(key);

    // Check cache size limit
    if (globalCache.size >= maxSize && !globalCache.has(cacheKey)) {
      // Remove oldest entry
      const oldestKey = globalCache.keys().next().value;
      globalCache.delete(oldestKey);
      cacheTimestamps.delete(oldestKey);
      warning('Cache size limit reached, removed oldest entry', {
        removedKey: oldestKey,
        newKey: cacheKey
      });
    }

    globalCache.set(cacheKey, value);
    cacheTimestamps.set(cacheKey, {
      time: Date.now(),
      ttl: ttl
    });

    cacheStats.value.sets++;
    cacheStats.value.size = globalCache.size;
    debug('Cache set', { key: cacheKey, ttl });
  };

  /**
   * Delete value from cache
   */
  const del = (key) => {
    const cacheKey = getCacheKey(key);
    const deleted = globalCache.delete(cacheKey);
    cacheTimestamps.delete(cacheKey);

    if (deleted) {
      cacheStats.value.deletes++;
      cacheStats.value.size = globalCache.size;
      debug('Cache delete', { key: cacheKey });
    }

    return deleted;
  };

  /**
   * Check if key exists in cache (and not expired)
   */
  const has = (key) => {
    const cacheKey = getCacheKey(key);
    return globalCache.has(cacheKey) && !isExpired(cacheKey);
  };

  /**
   * Clear all cache entries with the current prefix
   */
  const clear = () => {
    if (keyPrefix) {
      // Clear only entries with this prefix
      const keysToDelete = [];
      for (const key of globalCache.keys()) {
        if (key.startsWith(`${keyPrefix}:`)) {
          keysToDelete.push(key);
        }
      }

      keysToDelete.forEach(key => {
        globalCache.delete(key);
        cacheTimestamps.delete(key);
      });

      cacheStats.value.deletes += keysToDelete.length;
    } else {
      // Clear all cache
      globalCache.clear();
      cacheTimestamps.clear();
      cacheStats.value.deletes += cacheStats.value.size;
    }

    cacheStats.value.size = globalCache.size;
    debug('Cache cleared', { prefix: keyPrefix });
  };

  /**
   * Get or set pattern - fetch data if not in cache
   */
  const getOrSet = async (key, fetchFn, ttl = defaultTTL) => {
    const cached = get(key);
    if (cached !== null) {
      return cached;
    }

    try {
      const data = await fetchFn();
      set(key, data, ttl);
      return data;
    } catch (error) {
      debug('Cache fetch failed', { key, error: error.message });
      throw error;
    }
  };

  /**
   * Cleanup expired entries
   */
  const cleanup = () => {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, timestamp] of cacheTimestamps.entries()) {
      if (now - timestamp.time > timestamp.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      globalCache.delete(key);
      cacheTimestamps.delete(key);
    });

    if (expiredKeys.length > 0) {
      cacheStats.value.deletes += expiredKeys.length;
      cacheStats.value.size = globalCache.size;
      debug('Cache cleanup completed', {
        expiredCount: expiredKeys.length,
        remainingSize: globalCache.size
      });
    }
  };

  /**
   * Start automatic cleanup
   */
  const startAutoCleanup = () => {
    if (autoCleanup && !cleanupTimer) {
      cleanupTimer = setInterval(cleanup, cleanupInterval);
      debug('Cache auto-cleanup started', { interval: cleanupInterval });
    }
  };

  /**
   * Stop automatic cleanup
   */
  const stopAutoCleanup = () => {
    if (cleanupTimer) {
      clearInterval(cleanupTimer);
      cleanupTimer = null;
      debug('Cache auto-cleanup stopped');
    }
  };

  /**
   * Get cache statistics
   */
  const getStats = () => {
    return {
      ...cacheStats.value,
      hitRate: cacheStats.value.hits / (cacheStats.value.hits + cacheStats.value.misses) || 0
    };
  };

  /**
   * Get all keys with current prefix
   */
  const getKeys = () => {
    const keys = [];
    for (const key of globalCache.keys()) {
      if (!keyPrefix || key.startsWith(`${keyPrefix}:`)) {
        keys.push(keyPrefix ? key.substring(keyPrefix.length + 1) : key);
      }
    }
    return keys;
  };

  // Start auto cleanup on creation
  startAutoCleanup();

  // Cleanup on unmount
  onUnmounted(() => {
    stopAutoCleanup();
  });

  return {
    // Core methods
    get,
    set,
    del,
    has,
    clear,
    getOrSet,

    // Utility methods
    cleanup,
    startAutoCleanup,
    stopAutoCleanup,
    getStats,
    getKeys,

    // Stats (reactive)
    stats: cacheStats,
  };
}

/**
 * Specialized cache for API responses
 */
export function useApiCache(options = {}) {
  const {
    defaultTTL = 5 * 60 * 1000, // 5 minutes for API responses
    keyPrefix = 'api',
    ...cacheOptions
  } = options;

  return useCache({
    defaultTTL,
    keyPrefix,
    ...cacheOptions
  });
}

/**
 * Specialized cache for user session data
 */
export function useSessionCache(options = {}) {
  const {
    defaultTTL = 30 * 60 * 1000, // 30 minutes for session data
    keyPrefix = 'session',
    ...cacheOptions
  } = options;

  return useCache({
    defaultTTL,
    keyPrefix,
    ...cacheOptions
  });
}

/**
 * Specialized cache for assessment data
 */
export function useAssessmentCache(options = {}) {
  const {
    defaultTTL = 10 * 60 * 1000, // 10 minutes for assessment data
    keyPrefix = 'assessment',
    ...cacheOptions
  } = options;

  return useCache({
    defaultTTL,
    keyPrefix,
    ...cacheOptions
  });
}
