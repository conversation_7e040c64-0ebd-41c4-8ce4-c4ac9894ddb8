/**
 * Composable for managing session data
 */
import { ref, computed } from 'vue';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { extractResponseData, extractErrorInfo } from '@/utils/apiResponseHandler';
import { decodeSessionCodes } from '@/utils/hashIds';
import { useLoadingState } from './useUIState';

export function useSessions() {
  const loadingState = useLoadingState();
  const { message, isSuccess, setErrorMessage, clearMessage } = useMessageHandler();

  // Sessions data
  const pendingSessions = ref([]);
  const completedSessions = ref([]);
  const totalPendingSessions = ref(0);
  const totalCompletedSessions = ref(0);

  // Fetch pending sessions
  const fetchPendingSessions = async (page = 1, itemsPerPage = 3) => {
    loadingState.startLoading('Loading pending sessions...');
    clearMessage();

    try {
      const offset = (page - 1) * itemsPerPage;
      const params = {
        limit: itemsPerPage,
        offset: offset,
        status_filter: 'pending',
      };

      const response = await api.admin.getSessions(params);
      const responseData = extractResponseData(response);

      if (response.data?.meta?.pagination) {
        let sessions = responseData || [];
        sessions = await decodeSessionCodes(sessions);
        pendingSessions.value = sessions;
        totalPendingSessions.value = response.data.meta.pagination.total;
      } else {
        // Fallback for older response format
        const data = extractResponseData(response);
        if (data && data.sessions) {
          let sessions = data.sessions || [];
          sessions = await decodeSessionCodes(sessions);
          pendingSessions.value = sessions;
          totalPendingSessions.value = sessions.length;
        } else {
          pendingSessions.value = [];
          totalPendingSessions.value = 0;
          setErrorMessage('No session data received from server');
        }
      }
    } catch (error) {
      logError(error, 'fetchPendingSessions');
      const errorInfo = extractErrorInfo(error);
      setErrorMessage(errorInfo.message || 'Failed to fetch pending sessions');
    } finally {
      loadingState.stopLoading();
    }
  };

  // Fetch completed sessions
  const fetchCompletedSessions = async (page = 1, itemsPerPage = 3) => {
    loadingState.startLoading('Loading completed sessions...');
    clearMessage();

    try {
      const offset = (page - 1) * itemsPerPage;
      const params = {
        limit: itemsPerPage,
        offset: offset,
        status_filter: 'completed',
      };

      const response = await api.admin.getSessions(params);
      const responseData = extractResponseData(response);

      if (response.data?.meta?.pagination) {
        let sessions = responseData || [];
        sessions = await decodeSessionCodes(sessions);
        completedSessions.value = sessions;
        totalCompletedSessions.value = response.data.meta.pagination.total;
      } else {
        // Fallback for older response format
        const data = extractResponseData(response);
        if (data && data.sessions) {
          let allSessions = data.sessions || [];
          const filteredSessions = allSessions.filter(
            (session) => session.status === 'completed' || session.status === 'finished'
          );
          completedSessions.value = await decodeSessionCodes(filteredSessions);
          totalCompletedSessions.value = completedSessions.value.length;
        } else {
          completedSessions.value = [];
          totalCompletedSessions.value = 0;
        }
      }
    } catch (error) {
      logError(error, 'fetchCompletedSessions');
      const errorInfo = extractErrorInfo(error);
      setErrorMessage(errorInfo.message || 'Failed to fetch completed sessions');
    } finally {
      loadingState.stopLoading();
    }
  };

  return {
    ...loadingState,
    message,
    isSuccess,
    setErrorMessage,
    clearMessage,
    pendingSessions,
    completedSessions,
    totalPendingSessions,
    totalCompletedSessions,
    fetchPendingSessions,
    fetchCompletedSessions,
  };
}
