/**
 * Composables Index
 *
 * Central export file for all reusable composables
 * This allows for clean imports like: import { useModal, useFormValidation } from '@/composables'
 */

// Modal and Dialog Management
export {
  useModal,
  useDetailedResultsModal,
  useConfirmationModal,
} from './useModal';

// Form Validation
export {
  useFormValidation,
  useAssessmentFormValidation,
  useSkillFormValidation,
} from './useFormValidation';

// API Request Handling
export {
  useApiRequest,
  usePaginatedApiRequest,
  useSearchableApiRequest,
} from './useApiRequest';

// Navigation Utilities
export {
  useNavigation,
} from './useNavigation';

// Data Caching
export {
  useCache,
  useApiCache,
  useSessionCache,
  useAssessmentCache,
} from './useCache';

// UI State Management
export {
  useLoadingState,
  useSearch,
  usePagination,
  useTabs,
  useToggle,
  useSelection,
} from './useUIState';

// Event Handling
export {
  useKeyboardEvents,
  useEscapeKey,
  useClickOutside,
  useWindowResize,
  useScroll,
  useMousePosition,
  useFocusTrap,
} from './useEventHandlers';

// View-specific Composables
export { useSessions } from './useSessions';
export { useAssessments } from './useAssessments';
export { useSkillsForList } from './useSkillsForList';
export { useSkillsList } from './useSkillsList';
export { useUserSessions } from './useUserSessions';

// Form-specific Composables
export { useAssessmentForm } from './useAssessmentForm';
export { useQuestionSelection } from './useQuestionSelection';
